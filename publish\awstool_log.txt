2025-08-08 14:58:27 [信息] AWS自动注册工具启动
2025-08-08 14:58:27 [信息] 程序版本: 1.0.0.0
2025-08-08 14:58:27 [信息] 启动时间: 2025-08-08 14:58:27
2025-08-08 14:58:27 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-08 14:58:27 [信息] 线程数量已选择: 1
2025-08-08 14:58:27 [信息] 线程数量选择初始化完成
2025-08-08 14:58:27 [信息] 程序初始化完成
2025-08-08 14:58:32 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-08 14:58:35 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-08-智利.txt
2025-08-08 14:58:44 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-08-智利.txt
2025-08-08 14:58:44 [信息] 成功加载 21 条数据
2025-08-08 14:58:47 [信息] 线程数量已选择: 3
2025-08-08 15:04:15 [按钮操作] 开始注册 -> 启动注册流程
2025-08-08 15:04:15 [信息] 开始启动多线程注册，线程数量: 3
2025-08-08 15:04:15 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 21
2025-08-08 15:04:15 [信息] 所有线程已停止并清理
2025-08-08 15:04:15 [信息] 正在初始化多线程服务...
2025-08-08 15:04:15 [信息] 榴莲手机API服务已初始化
2025-08-08 15:04:15 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-08 15:04:15 [信息] 多线程服务初始化完成
2025-08-08 15:04:15 [信息] 数据分配完成：共21条数据分配给3个线程
2025-08-08 15:04:15 [信息] 线程1分配到7条数据
2025-08-08 15:04:15 [信息] 线程2分配到7条数据
2025-08-08 15:04:15 [信息] 线程3分配到7条数据
2025-08-08 15:04:15 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:04:15 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:04:15 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:04:15 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:04:15 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-08 15:04:15 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:04:15 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:04:15 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:04:15 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:04:15 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:04:15 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=16 GB
2025-08-08 15:04:15 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:04:15 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:04:15 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:04:15 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:04:15 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:04:15 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=6 GB
2025-08-08 15:04:15 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:04:15 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:04:15 [信息] 多线程注册启动成功，共3个线程
2025-08-08 15:04:15 线程1：[信息] 开始启动注册流程
2025-08-08 15:04:15 线程2：[信息] 开始启动注册流程
2025-08-08 15:04:15 线程3：[信息] 开始启动注册流程
2025-08-08 15:04:15 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-08 15:04:15 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-08 15:04:15 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:04:15 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:04:15 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-08 15:04:15 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:04:15 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:04:15 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:04:15 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:04:15 [信息] 多线程管理窗口已初始化
2025-08-08 15:04:15 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:04:15 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-08 15:04:15 [信息] 多线程管理窗口已打开
2025-08-08 15:04:15 [信息] 多线程注册启动成功，共3个线程
2025-08-08 15:04:20 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:04:20 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-08 15:04:20 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:04:20 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:04:20 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-08 15:04:20 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:04:21 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:04:21 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-08 15:04:21 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:04:21 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:04:21 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-08 15:04:21 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:04:21 [信息] UniformGrid列数已更新为: 2
2025-08-08 15:04:21 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-08 15:04:21 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:04:21 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:04:21 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-08 15:04:21 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:04:23 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:04:23 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:04:23 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:04:25 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:04:25 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:04:25 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-08 15:04:25 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 32核 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=16 GB
2025-08-08 15:04:25 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:04:25 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:04:25 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-08 15:04:25 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:04:25 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:04:25 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-08 15:04:25 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 8核 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-08 15:04:25 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 4核 (进度: 0%)
2025-08-08 15:04:25 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=6 GB
2025-08-08 15:04:26 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:04:27 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:04:27 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:04:29 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: DE-F0-12-34-56-78
   • 屏幕分辨率: 1926x1120
   • 可用区域: 1926x1080

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: unknown
   • 电池API支持: True
   • 电池电量: 0.93
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:04:29 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: DE-F0-12-34-56-78    • 屏幕分辨率: 1926x1120    • 可用区域: 1926x1080   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: unknown    • 电池API支持: True    • 电池电量: 0.93    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:04:29 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_007
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 1918x1089
   • 可用区域: 1918x1049

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.75
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:04:29 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_007    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 1918x1089    • 可用区域: 1918x1049   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.75    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:04:29 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: AA-BB-CC-DD-EE-FF
   • 屏幕分辨率: 1867x1024
   • 可用区域: 1867x984

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.37
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:04:29 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: AA-BB-CC-DD-EE-FF    • 屏幕分辨率: 1867x1024    • 可用区域: 1867x984   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.37    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-08 15:04:29 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-08 15:04:29 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:04:29 线程2：[信息] 浏览器启动成功
2025-08-08 15:04:29 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:04:29 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:04:29 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:04:29 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:04:29 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:04:29 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:04:29 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:04:29 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-08 15:04:29 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:04:29 线程3：[信息] 浏览器启动成功
2025-08-08 15:04:29 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:04:30 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:04:30 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:04:30 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:04:30 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:04:30 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:04:30 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:04:30 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-08 15:04:30 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:04:30 线程1：[信息] 浏览器启动成功
2025-08-08 15:04:30 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:04:31 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:04:31 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:04:31 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-08 15:04:31 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-08 15:04:31 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:04:31 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-08 15:04:31 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-08 15:04:31 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:04:31 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:04:31 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:04:31 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:04:31 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:04:31 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:04:31 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:04:31 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:05:01 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-08 15:05:01 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-08 15:05:01 [信息] 第一页相关失败，数据保持不动
2025-08-08 15:05:01 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:05:01 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:05:01 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-08 15:05:01 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-08 15:05:01 [信息] 第一页相关失败，数据保持不动
2025-08-08 15:05:01 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:05:01 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:05:01 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-08 15:05:01 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:05:01 [信息] 多线程状态已重置
2025-08-08 15:05:01 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:05:01 [信息] 多线程状态已重置
2025-08-08 15:05:01 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-08 15:05:01 [信息] 第一页相关失败，数据保持不动
2025-08-08 15:05:01 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:05:01 [信息] 多线程状态已重置
2025-08-08 15:05:01 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:05:01 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-08 15:06:44 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:44 [信息] 多线程状态已重置
2025-08-08 15:06:44 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-08 15:06:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:06:45 [信息] 多线程状态已重置
2025-08-08 15:06:45 线程3：[信息] [信息] 所有自动线程已停止 (进度: 0%)
2025-08-08 15:06:45 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 0%)
2025-08-08 15:06:45 线程3：[信息] 已暂停
2025-08-08 15:06:45 [信息] 线程3已暂停
2025-08-08 15:06:45 [信息] 线程3已暂停
2025-08-08 15:06:46 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-08 15:06:46 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-08 15:06:46 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-08 15:06:46 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-08 15:06:46 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:06:46 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-08 15:06:46 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:06:46 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-08 15:06:46 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-08 15:06:49 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-08 15:06:49 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-08 15:06:49 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-08 15:06:49 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-08 15:06:49 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-08 15:06:49 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-08 15:06:49 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-08 15:06:49 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-08 15:06:49 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:06:49 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-08 15:06:49 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:06:49 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Google，服务状态=True (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Google），等待2秒后开始获取验证码... (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] 已继续
2025-08-08 15:06:51 [信息] 线程2已继续
2025-08-08 15:06:51 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Google，服务状态=True (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Google），等待2秒后开始获取验证码... (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-08 15:06:51 线程2：[信息] [信息] 开始使用谷歌API获取验证码，订单ID: 9DEuI5d07 (进度: 100%)
2025-08-08 15:06:51 线程1：[信息] 已继续
2025-08-08 15:06:51 [信息] 线程1已继续
2025-08-08 15:06:51 线程1：[信息] [信息] 开始使用谷歌API获取验证码，订单ID: G5R2ssksw (进度: 100%)
2025-08-08 15:08:52 线程1：[信息] [信息] 谷歌API获取验证码超时（2分钟），转为手动模式 (进度: 100%)
2025-08-08 15:08:52 线程2：[信息] [信息] 谷歌API获取验证码超时（2分钟），转为手动模式 (进度: 100%)
2025-08-08 15:08:52 线程2：[信息] [信息] 邮箱验证码自动获取失败: 获取超时 (进度: 100%)
2025-08-08 15:08:52 线程1：[信息] [信息] 邮箱验证码自动获取失败: 获取超时 (进度: 100%)
2025-08-08 15:08:52 线程2：[信息] [信息] 🔴 Google获取验证码失败，转为手动模式 (进度: 100%)
2025-08-08 15:08:52 线程1：[信息] [信息] 🔴 Google获取验证码失败，转为手动模式 (进度: 100%)
2025-08-08 15:09:06 [信息] 多线程窗口引用已清理
2025-08-08 15:09:06 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-08 15:09:06 [信息] 多线程管理窗口正在关闭
2025-08-08 15:09:09 [信息] 程序正在退出，开始清理工作...
2025-08-08 15:09:09 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-08 15:09:09 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-08 15:09:09 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-08 15:09:09 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-08 15:09:09 [信息] 程序退出清理工作完成
2025-08-08 15:09:28 [信息] AWS自动注册工具启动
2025-08-08 15:09:28 [信息] 程序版本: 1.0.0.0
2025-08-08 15:09:28 [信息] 启动时间: 2025-08-08 15:09:28
2025-08-08 15:09:28 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-08 15:09:28 [信息] 线程数量已选择: 1
2025-08-08 15:09:28 [信息] 线程数量选择初始化完成
2025-08-08 15:09:28 [信息] 程序初始化完成
2025-08-08 15:09:30 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-08 15:09:32 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-08-智利.txt
2025-08-08 15:09:33 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-08-智利.txt
2025-08-08 15:09:33 [信息] 成功加载 21 条数据
2025-08-08 15:09:35 [信息] 线程数量已选择: 3
2025-08-08 15:09:39 [按钮操作] 开始注册 -> 启动注册流程
2025-08-08 15:09:39 [信息] 开始启动多线程注册，线程数量: 3
2025-08-08 15:09:39 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 21
2025-08-08 15:09:39 [信息] 所有线程已停止并清理
2025-08-08 15:09:39 [信息] 正在初始化多线程服务...
2025-08-08 15:09:39 [信息] 榴莲手机API服务已初始化
2025-08-08 15:09:39 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-08 15:09:39 [信息] 多线程服务初始化完成
2025-08-08 15:09:39 [信息] 数据分配完成：共21条数据分配给3个线程
2025-08-08 15:09:39 [信息] 线程1分配到7条数据
2025-08-08 15:09:39 [信息] 线程2分配到7条数据
2025-08-08 15:09:39 [信息] 线程3分配到7条数据
2025-08-08 15:09:39 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:09:39 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:09:39 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:09:39 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:09:39 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=16 GB
2025-08-08 15:09:39 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:09:39 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:09:39 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:09:39 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:09:39 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:09:39 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=4 GB
2025-08-08 15:09:39 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:09:39 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:09:39 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:09:39 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:09:39 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:09:39 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_004, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=12 GB
2025-08-08 15:09:39 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:09:39 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:09:39 [信息] 多线程注册启动成功，共3个线程
2025-08-08 15:09:39 线程1：[信息] 开始启动注册流程
2025-08-08 15:09:39 线程2：[信息] 开始启动注册流程
2025-08-08 15:09:39 线程3：[信息] 开始启动注册流程
2025-08-08 15:09:39 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-08 15:09:39 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-08 15:09:39 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:09:39 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:09:39 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-08 15:09:39 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:09:39 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:09:39 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:09:39 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:09:39 [信息] 多线程管理窗口已初始化
2025-08-08 15:09:39 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:09:39 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-08 15:09:39 [信息] 多线程管理窗口已打开
2025-08-08 15:09:39 [信息] 多线程注册启动成功，共3个线程
2025-08-08 15:09:43 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:09:43 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-08 15:09:43 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:09:43 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:09:43 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-08 15:09:43 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:09:44 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:09:44 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-08 15:09:44 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:09:44 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:09:44 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-08 15:09:45 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:09:45 [信息] UniformGrid列数已更新为: 2
2025-08-08 15:09:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-08 15:09:45 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:09:45 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:09:45 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-08 15:09:45 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:09:46 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:09:46 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:09:46 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:09:48 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:09:48 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:09:48 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-08 15:09:48 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 4核 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=16 GB
2025-08-08 15:09:48 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:09:48 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:09:48 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-08 15:09:48 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:09:48 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:09:48 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-08 15:09:48 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:09:48 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 2核 (进度: 0%)
2025-08-08 15:09:48 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=4 GB
2025-08-08 15:09:49 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_004, CPU: 4核 (进度: 0%)
2025-08-08 15:09:49 [信息] 浏览器指纹注入: Canvas=canvas_fp_004, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=12 GB
2025-08-08 15:09:50 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:09:50 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:09:51 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: AA-BB-CC-DD-EE-FF
   • 屏幕分辨率: 1729x1030
   • 可用区域: 1729x990

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.35
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:09:51 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: AA-BB-CC-DD-EE-FF    • 屏幕分辨率: 1729x1030    • 可用区域: 1729x990   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.35    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:09:51 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: user

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_009
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1722x1003
   • 可用区域: 1722x963

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.88
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:09:51 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: user   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_009    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1722x1003    • 可用区域: 1722x963   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.88    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:09:51 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: auto

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Y9Z0A1B
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 2015x1047
   • 可用区域: 2015x1007

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.50
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:09:51 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: auto   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Y9Z0A1B    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 2015x1047    • 可用区域: 2015x1007   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.50    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:09:51 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-08 15:09:51 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:09:51 线程1：[信息] 浏览器启动成功
2025-08-08 15:09:51 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:09:52 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:09:52 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:09:52 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:09:52 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:09:52 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:09:52 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:09:52 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-08 15:09:52 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:09:52 线程2：[信息] 浏览器启动成功
2025-08-08 15:09:52 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:09:52 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:09:52 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:09:52 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:09:52 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:09:52 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:09:52 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:09:52 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-08 15:09:52 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:09:52 线程3：[信息] 浏览器启动成功
2025-08-08 15:09:52 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:09:52 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:09:53 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:09:53 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:09:53 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:09:53 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:09:53 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:09:55 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-08 15:09:55 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-08 15:09:55 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-08 15:09:55 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:09:55 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-08 15:09:55 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-08 15:09:55 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:09:55 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:09:56 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:09:56 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:09:56 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-08 15:09:56 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:09:56 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:09:56 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:09:56 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-08 15:09:56 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:09:56 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:09:56 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:10:28 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-08 15:10:28 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-08 15:10:28 [信息] 第一页相关失败，数据保持不动
2025-08-08 15:10:28 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:10:28 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:10:29 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-08 15:10:29 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-08 15:10:29 [信息] 第一页相关失败，数据保持不动
2025-08-08 15:10:29 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:10:29 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:10:29 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-08 15:10:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:10:29 [信息] 多线程状态已重置
2025-08-08 15:10:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:10:29 [信息] 多线程状态已重置
2025-08-08 15:10:29 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-08 15:10:29 [信息] 第一页相关失败，数据保持不动
2025-08-08 15:10:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:10:29 [信息] 多线程状态已重置
2025-08-08 15:10:29 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:10:29 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-08 15:13:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:13:04 [信息] 多线程状态已重置
2025-08-08 15:13:04 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-08 15:13:04 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:13:04 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-08 15:13:04 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:13:04 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-08 15:13:04 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-08 15:13:04 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-08 15:13:04 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:13:04 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-08 15:13:04 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:13:04 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-08 15:13:05 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-08 15:13:05 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-08 15:13:07 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-08 15:13:07 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-08 15:13:07 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-08 15:13:07 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-08 15:13:07 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:13:07 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-08 15:13:08 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-08 15:13:08 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-08 15:13:08 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-08 15:13:08 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-08 15:13:08 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-08 15:13:08 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-08 15:13:08 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-08 15:13:08 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-08 15:13:08 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:13:08 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-08 15:13:08 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:13:08 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-08 15:13:09 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-08 15:13:10 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:13:10 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-08 15:13:10 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-08 15:13:10 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-08 15:13:10 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-08 15:13:10 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-08 15:13:10 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-08 15:13:10 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:13:18 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35514 字节 (进度: 100%)
2025-08-08 15:13:18 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35514字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:13:18 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:13:19 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35101 字节 (进度: 100%)
2025-08-08 15:13:19 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35101字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:13:19 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:13:19 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35303 字节 (进度: 100%)
2025-08-08 15:13:19 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35303字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:13:19 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:13:20 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"g4ns26"},"taskId":"29f6c686-7427-11f0-9a8e-ae88852438b9"} (进度: 100%)
2025-08-08 15:13:20 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"w65sxz"},"taskId":"29fc29dc-7427-11f0-b0c6-263b5469d4bd"} (进度: 100%)
2025-08-08 15:13:20 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"tyxbdx"},"taskId":"29f2bb2c-7427-11f0-9392-c60a782a5790"} (进度: 100%)
2025-08-08 15:13:20 线程2：[信息] [信息] 第一页第1次识别结果: g4ns26 → 转换为小写: g4ns26 (进度: 100%)
2025-08-08 15:13:20 线程1：[信息] [信息] 第一页第1次识别结果: w65sxz → 转换为小写: w65sxz (进度: 100%)
2025-08-08 15:13:20 线程3：[信息] [信息] 第一页第1次识别结果: tyxbdx → 转换为小写: tyxbdx (进度: 100%)
2025-08-08 15:13:20 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:13:20 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:13:20 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:13:20 线程2：[信息] [信息] 已填入验证码: g4ns26 (进度: 100%)
2025-08-08 15:13:20 线程1：[信息] [信息] 已填入验证码: w65sxz (进度: 100%)
2025-08-08 15:13:20 线程3：[信息] [信息] 已填入验证码: tyxbdx (进度: 100%)
2025-08-08 15:13:20 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:13:20 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:13:20 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:13:22 线程2：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-08 15:13:22 线程2：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-08 15:13:22 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:13:22 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-08 15:13:22 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-08 15:13:22 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-08 15:13:23 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:13:23 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-08 15:13:23 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-08 15:13:23 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-08 15:13:23 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-08 15:13:23 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-08 15:13:23 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-08 15:13:23 线程3：[信息] 已继续
2025-08-08 15:13:23 [信息] 线程3已继续
2025-08-08 15:13:23 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-08 15:13:23 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-08 15:13:23 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-08 15:13:23 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-08 15:13:23 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-08 15:13:23 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-08 15:13:23 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-08 15:13:23 线程1：[信息] 已继续
2025-08-08 15:13:23 [信息] 线程1已继续
2025-08-08 15:13:24 线程2：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:13:25 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:25 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:25 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-08 15:13:25
2025-08-08 15:13:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-08 15:13:25
2025-08-08 15:13:28 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31000 字节 (进度: 100%)
2025-08-08 15:13:28 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，31000字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:13:28 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:13:28 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:28 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-08 15:13:28
2025-08-08 15:13:28 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-08 15:13:28
2025-08-08 15:13:28 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"nds6bh"},"taskId":"2eefa234-7427-11f0-b0c6-263b5469d4bd"} (进度: 100%)
2025-08-08 15:13:28 线程2：[信息] [信息] 第一页第2次识别结果: nds6bh → 转换为小写: nds6bh (进度: 100%)
2025-08-08 15:13:28 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:13:28 线程2：[信息] [信息] 已填入验证码: nds6bh (进度: 100%)
2025-08-08 15:13:29 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 第一页第2次图形验证码识别成功 (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-08 15:13:31 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-08 15:13:31 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-08 15:13:31 线程2：[信息] 已继续
2025-08-08 15:13:31 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-08 15:13:31 [信息] 线程2已继续
2025-08-08 15:13:31 [信息] 继续了 3 个可继续的线程
2025-08-08 15:13:31 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:31 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-08 15:13:31
2025-08-08 15:13:31 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:31 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-08 15:13:31
2025-08-08 15:13:31 [信息] [线程1] 邮箱验证码获取成功: 857308，立即停止重复请求
2025-08-08 15:13:31 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-08 15:13:31 [信息] [线程1] 已清理响应文件
2025-08-08 15:13:31 线程1：[信息] [信息] 验证码获取成功: 857308，正在自动填入... (进度: 100%)
2025-08-08 15:13:31 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-08 15:13:31 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-08 15:13:31 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-08 15:13:31 [信息] 线程1完成第二页事件已处理
2025-08-08 15:13:31 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-08 15:13:31 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-08 15:13:31 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-08 15:13:31 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-08 15:13:31 [信息] [线程3] 邮箱验证码获取成功: 000289，立即停止重复请求
2025-08-08 15:13:31 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-08 15:13:31 [信息] [线程3] 已清理响应文件
2025-08-08 15:13:31 线程3：[信息] [信息] 验证码获取成功: 000289，正在自动填入... (进度: 100%)
2025-08-08 15:13:31 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-08 15:13:31 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-08 15:13:31 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-08 15:13:31 [信息] 线程3完成第二页事件已处理
2025-08-08 15:13:31 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-08 15:13:31 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-08 15:13:32 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+526648328770","+528461093100","+529513658096"]}
2025-08-08 15:13:32 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-08 15:13:32 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-08 15:13:32 [信息] 线程1分配榴莲手机号码: +526648328770
2025-08-08 15:13:32 [信息] 线程2分配榴莲手机号码: +528461093100
2025-08-08 15:13:32 [信息] 线程3分配榴莲手机号码: +529513658096
2025-08-08 15:13:32 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-08 15:13:32 [信息] 批量获取3个手机号码成功
2025-08-08 15:13:33 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:33 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-08 15:13:33
2025-08-08 15:13:34 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-08 15:13:34 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-08 15:13:34 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-08 15:13:34 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-08 15:13:34 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-08 15:13:34 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-08 15:13:34 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-08 15:13:34 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-08 15:13:34 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-08 15:13:34 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-08 15:13:35 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-08 15:13:36 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-08 15:13:36 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:36 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-08 15:13:36
2025-08-08 15:13:38 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-08 15:13:38 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-08 15:13:38 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-08 15:13:39 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-08 15:13:39 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-08 15:13:39 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-08 15:13:39 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-08 15:13:39 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-08 15:13:39
2025-08-08 15:13:41 [信息] [线程2] 邮箱验证码获取成功: 794743，立即停止重复请求
2025-08-08 15:13:41 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-08 15:13:41 [信息] [线程2] 已清理响应文件
2025-08-08 15:13:41 线程2：[信息] [信息] 验证码获取成功: 794743，正在自动填入... (进度: 100%)
2025-08-08 15:13:41 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-08 15:13:41 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-08 15:13:41 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-08 15:13:41 [信息] 线程2完成第二页事件已处理
2025-08-08 15:13:41 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-08 15:13:41 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-08 15:13:44 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-08 15:13:44 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-08 15:13:45 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-08 15:13:45 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-08 15:13:45 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-08 15:13:45 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-08 15:13:45 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-08 15:13:45 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-08 15:13:45 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-08 15:13:46 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-08 15:13:49 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-08 15:13:49 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-08 15:13:49 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-08 15:13:54 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-08 15:13:54 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-08 15:14:01 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-08 15:14:01 [信息] 线程1获取已分配的榴莲手机号码: +526648328770
2025-08-08 15:14:01 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +526648328770 (进度: 100%)
2025-08-08 15:14:02 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-08 15:14:02 [信息] 线程3获取已分配的榴莲手机号码: +529513658096
2025-08-08 15:14:02 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +529513658096 (进度: 100%)
2025-08-08 15:14:02 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-08 15:14:02 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-08 15:14:02 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-08 15:14:02 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-08 15:14:04 线程1：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-08 15:14:04 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-08 15:14:04 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-08 15:14:04 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:14:04 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-08 15:14:04 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-08 15:14:04 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-08 15:14:04 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:14:07 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-08 15:14:07 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-08 15:14:08 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-08 15:14:08 线程1：[信息] [信息] 已自动获取并填入手机号码: +526648328770 (进度: 100%)
2025-08-08 15:14:08 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-08 15:14:08 线程3：[信息] [信息] 已自动获取并填入手机号码: +529513658096 (进度: 100%)
2025-08-08 15:14:09 线程1：[信息] [信息] 使用已获取的手机号码: +526648328770（保存本地号码: +526648328770） (进度: 100%)
2025-08-08 15:14:09 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-08 15:14:09 线程3：[信息] [信息] 使用已获取的手机号码: +529513658096（保存本地号码: +529513658096） (进度: 100%)
2025-08-08 15:14:09 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-08 15:14:12 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-08 15:14:12 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-08 15:14:12 [信息] 线程2获取已分配的榴莲手机号码: +528461093100
2025-08-08 15:14:12 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +528461093100 (进度: 100%)
2025-08-08 15:14:12 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-08 15:14:13 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-08 15:14:13 线程1：[信息] [信息] 正在选择月份: January (进度: 100%)
2025-08-08 15:14:13 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-08 15:14:13 线程1：[信息] [信息] 已选择月份（标准选项）: January (进度: 100%)
2025-08-08 15:14:14 线程3：[信息] [信息] 正在选择月份: June (进度: 100%)
2025-08-08 15:14:14 线程3：[信息] [信息] 已选择月份（标准选项）: June (进度: 100%)
2025-08-08 15:14:14 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-08 15:14:14 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-08 15:14:14 线程3：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-08 15:14:15 线程3：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-08 15:14:15 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-08 15:14:15 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-08 15:14:15 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-08 15:14:15 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-08 15:14:15 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-08 15:14:15 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-08 15:14:15 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:14:15 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-08 15:14:15 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-08 15:14:15 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-08 15:14:19 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-08 15:14:19 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:14:19 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-08 15:14:19 线程2：[信息] [信息] 已自动获取并填入手机号码: +528461093100 (进度: 100%)
2025-08-08 15:14:19 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:14:20 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-08 15:14:20 线程1：[信息] [信息] 已清空并重新填写手机号码: +526648328770 (进度: 100%)
2025-08-08 15:14:20 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-08 15:14:20 线程2：[信息] [信息] 使用已获取的手机号码: +528461093100（保存本地号码: +528461093100） (进度: 100%)
2025-08-08 15:14:20 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-08 15:14:21 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-08 15:14:21 线程3：[信息] [信息] 已清空并重新填写手机号码: +529513658096 (进度: 100%)
2025-08-08 15:14:21 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-08 15:14:22 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-08 15:14:22 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-08 15:14:22 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-08 15:14:22 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-08 15:14:22 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-08 15:14:23 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-08 15:14:23 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-08 15:14:23 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-08 15:14:23 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-08 15:14:23 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-08 15:14:24 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-08 15:14:24 线程2：[信息] [信息] 正在选择月份: July (进度: 100%)
2025-08-08 15:14:24 线程2：[信息] [信息] 已选择月份（标准选项）: July (进度: 100%)
2025-08-08 15:14:25 线程2：[信息] [信息] 正在选择年份: 2030 (进度: 100%)
2025-08-08 15:14:25 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-08 15:14:25 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:14:25 线程2：[信息] [信息] 已选择年份（标准选项）: 2030 (进度: 100%)
2025-08-08 15:14:26 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-08 15:14:26 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:14:26 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-08 15:14:26 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-08 15:14:26 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-08 15:14:28 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34452 字节 (进度: 100%)
2025-08-08 15:14:28 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34452字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:14:28 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:14:29 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35349 字节 (进度: 100%)
2025-08-08 15:14:29 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，35349字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:14:29 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:14:29 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"pbsmg5"},"taskId":"531ae3e4-7427-11f0-9cc4-ae88852438b9"} (进度: 100%)
2025-08-08 15:14:29 线程1：[信息] [信息] 第六页第1次识别结果: pbsmg5 → 转换为小写: pbsmg5 (进度: 100%)
2025-08-08 15:14:29 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:14:29 线程1：[信息] [信息] 第六页已填入验证码: pbsmg5 (进度: 100%)
2025-08-08 15:14:29 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:14:30 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"c5fmtc"},"taskId":"53850d46-7427-11f0-8e95-263b5469d4bd"} (进度: 100%)
2025-08-08 15:14:30 线程3：[信息] [信息] 第六页第1次识别结果: c5fmtc → 转换为小写: c5fmtc (进度: 100%)
2025-08-08 15:14:30 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:14:30 线程3：[信息] [信息] 第六页已填入验证码: c5fmtc (进度: 100%)
2025-08-08 15:14:30 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:14:31 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:14:32 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-08 15:14:32 线程2：[信息] [信息] 已清空并重新填写手机号码: +528461093100 (进度: 100%)
2025-08-08 15:14:32 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-08 15:14:32 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:14:32 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-08 15:14:33 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:14:33 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-08 15:14:34 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-08 15:14:34 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-08 15:14:34 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-08 15:14:34 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-08 15:14:34 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-08 15:14:36 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-08 15:14:37 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-08 15:14:37 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:14:37 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-08 15:14:39 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-08 15:14:39 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:14:39 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:14:39 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-08 15:14:41 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-08 15:14:41 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:14:41 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:14:41 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-08 15:14:41 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34831 字节 (进度: 100%)
2025-08-08 15:14:41 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34831字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:14:41 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:14:42 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"b6pc3w"},"taskId":"5aeb4c80-7427-11f0-9392-c60a782a5790"} (进度: 100%)
2025-08-08 15:14:42 线程2：[信息] [信息] 第六页第1次识别结果: b6pc3w → 转换为小写: b6pc3w (进度: 100%)
2025-08-08 15:14:42 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:14:42 线程2：[信息] [信息] 第六页已填入验证码: b6pc3w (进度: 100%)
2025-08-08 15:14:43 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:14:44 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-08 15:14:44 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-08 15:14:44 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-08 15:14:44 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:14:44 线程1：[信息] [信息] 线程1验证码获取成功: 3716 (进度: 100%)
2025-08-08 15:14:44 [信息] 线程1手机号码已加入释放队列: +526648328770 (原因: 获取验证码成功)
2025-08-08 15:14:44 线程1：[信息] [信息] 线程1验证码获取成功: 3716，立即填入验证码... (进度: 100%)
2025-08-08 15:14:45 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-08 15:14:45 线程1：[信息] [信息] 线程1已自动填入手机验证码: 3716 (进度: 100%)
2025-08-08 15:14:46 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:14:46 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-08 15:14:46 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-08 15:14:46 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-08 15:14:46 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-08 15:14:46 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-08 15:14:46 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-08 15:14:46 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:14:46 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:14:46 线程3：[信息] [信息] 线程3第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:14:46 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:14:49 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-08 15:14:49 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-08 15:14:49 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-08 15:14:49 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-08 15:14:50 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-08 15:14:52 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-08 15:14:52 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:14:52 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:14:52 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-08 15:14:53 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-08 15:14:53 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-08 15:14:54 线程3：[信息] [信息] 线程3第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-08 15:14:54 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:14:55 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:14:55 线程3：[信息] [信息] 线程3第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:14:55 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:14:58 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-08 15:14:58 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-08 15:14:58 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-08 15:14:58 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:14:59 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-08 15:14:59 线程2：[信息] [信息] 线程2验证码获取成功: 5415 (进度: 100%)
2025-08-08 15:14:59 [信息] 线程2手机号码已加入释放队列: +528461093100 (原因: 获取验证码成功)
2025-08-08 15:14:59 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-08 15:14:59 线程2：[信息] [信息] 线程2验证码获取成功: 5415，立即填入验证码... (进度: 100%)
2025-08-08 15:14:59 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-08 15:14:59 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-08 15:14:59 线程2：[信息] [信息] 线程2已自动填入手机验证码: 5415 (进度: 100%)
2025-08-08 15:15:00 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-08 15:15:00 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-08 15:15:03 线程3：[信息] [信息] 线程3第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-08 15:15:03 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:15:03 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-08 15:15:03 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:15:03 线程3：[信息] [信息] 线程3第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:15:03 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:15:03 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-08 15:15:04 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-08 15:15:04 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-08 15:15:06 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-08 15:15:06 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-08 15:15:07 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-08 15:15:07 [信息] 成功点击更多按钮
2025-08-08 15:15:07 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-08 15:15:07 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-08 15:15:12 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-08 15:15:12 [信息] 开始释放2个手机号码
2025-08-08 15:15:12 [信息] [手机API] 开始批量释放2个手机号码
2025-08-08 15:15:12 [信息] [手机API] 释放手机号码: +526648328770
2025-08-08 15:15:12 线程3：[信息] [信息] 线程3第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-08 15:15:12 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:15:12 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-08 15:15:12 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-08 15:15:12 [信息] 成功点击账户信息按钮
2025-08-08 15:15:13 [信息] [手机API] 手机号码释放成功: +526648328770
2025-08-08 15:15:13 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:15:13 线程3：[信息] [信息] 线程3第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:15:13 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:15:13 [信息] [手机API] 释放手机号码: +528461093100
2025-08-08 15:15:13 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-08 15:15:13 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-08 15:15:13 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-08 15:15:13 [信息] 成功定位到'安全凭证'链接
2025-08-08 15:15:14 [信息] [手机API] 手机号码释放成功: +528461093100
2025-08-08 15:15:14 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-08 15:15:14 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-08 15:15:16 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-08 15:15:16 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-08 15:15:16 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-08 15:15:20 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-08 15:15:20 [信息] 成功点击'安全凭证'链接
2025-08-08 15:15:20 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-08 15:15:23 线程3：[信息] [信息] 线程3第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-08 15:15:23 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:15:23 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:15:23 线程3：[信息] [信息] 线程3第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:15:23 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:15:24 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-08 15:15:24 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-08 15:15:24 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-08 15:15:24 [信息] 检测到账单问题，开始处理
2025-08-08 15:15:24 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：G5R2ssksw ③AWS密码：J7Jpr37O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-08 15:15:24 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：G5R2ssksw ③AWS密码：J7Jpr37O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:24 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：G5R2ssksw ③AWS密码：J7Jpr37O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:24 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：G5R2ssksw ③AWS密码：J7Jpr37O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:24 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：G5R2ssksw ③AWS密码：J7Jpr37O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:24 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：G5R2ssksw ③AWS密码：J7Jpr37O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:24 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-08 15:15:24 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-08 15:15:24 [信息] 注册完成 - 账单提示处理
2025-08-08 15:15:24 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-08 15:15:24 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-08 15:15:24 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-08 15:15:24 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-08 15:15:24 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-08 15:15:24 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-08 15:15:24 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-08 15:15:30 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-08 15:15:30 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-08 15:15:30 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-08 15:15:30 [信息] 成功点击更多按钮
2025-08-08 15:15:31 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-08 15:15:31 线程3：[信息] [信息] 线程3第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-08 15:15:31 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:15:31 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-08 15:15:31 [信息] 成功点击账户信息按钮
2025-08-08 15:15:32 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:15:32 线程3：[信息] [信息] 线程3第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:15:32 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:15:32 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-08 15:15:32 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-08 15:15:33 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-08 15:15:33 [信息] 成功定位到'安全凭证'链接
2025-08-08 15:15:38 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-08 15:15:38 [信息] 成功点击'安全凭证'链接
2025-08-08 15:15:38 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-08 15:15:40 线程3：[信息] [信息] 线程3第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-08 15:15:40 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:15:40 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:15:40 线程3：[信息] [信息] 线程3第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:15:40 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-08 15:15:42 线程2：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-08 15:15:42 线程2：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-08 15:15:42 线程2：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-08 15:15:42 [信息] 检测到账单问题，开始处理
2025-08-08 15:15:43 线程2：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9DEuI5d07 ③AWS密码：NNQr0ncY ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-08 15:15:43 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9DEuI5d07 ③AWS密码：NNQr0ncY ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:43 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：9DEuI5d07 ③AWS密码：NNQr0ncY ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:43 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：9DEuI5d07 ③AWS密码：NNQr0ncY ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:43 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：9DEuI5d07 ③AWS密码：NNQr0ncY ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:43 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：9DEuI5d07 ③AWS密码：NNQr0ncY ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-08 15:15:43 线程2：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-08 15:15:43 线程2：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-08 15:15:43 [信息] 注册完成 - 账单提示处理
2025-08-08 15:15:43 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-08 15:15:43 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-08 15:15:43 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-08 15:15:43 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-08 15:15:43 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-08 15:15:43 线程2：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-08 15:15:43 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-08 15:15:48 线程3：[信息] [信息] 线程3第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-08 15:15:48 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:15:49 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-08 15:15:49 线程3：[信息] [信息] 线程3第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-08 15:15:49 线程3：[信息] [信息] 线程3验证码获取失败，已尝试8次 (进度: 100%)
2025-08-08 15:15:49 [信息] 线程3手机号码已加入释放队列: +529513658096 (原因: 验证码获取失败)
2025-08-08 15:15:49 线程3：[信息] [信息] 线程3验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-08 15:15:49 线程3：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-08 15:15:49 线程3：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-08 15:15:49 线程3：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-08 15:15:49 线程3：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-08 15:15:49 线程3：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-08 15:15:52 线程3：[信息] [信息] 正在选择国家代码: +52 (进度: 100%)
2025-08-08 15:15:52 线程3：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-08 15:15:53 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%) (进度: 100%)
2025-08-08 15:15:53 线程3：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-08 15:15:53 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-08 15:15:54 线程3：[信息] [信息] 后台获取新手机号码成功: +526863914468，已保存到注册数据 (进度: 100%)
2025-08-08 15:15:54 线程3：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-08 15:15:54 线程3：[信息] [信息] 已填入新手机号码: +526863914468 (进度: 100%)
2025-08-08 15:15:54 线程3：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-08 15:15:56 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-08 15:15:56 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-08 15:15:57 线程3：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-08 15:15:57 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-08 15:16:00 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-08 15:16:00 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:16:03 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34888 字节 (进度: 100%)
2025-08-08 15:16:03 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34888字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:16:03 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:16:04 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"2m7gm3"},"taskId":"8b8ee54a-7427-11f0-b0c6-263b5469d4bd"} (进度: 100%)
2025-08-08 15:16:04 线程3：[信息] [信息] 第六页第1次识别结果: 2m7gm3 → 转换为小写: 2m7gm3 (进度: 100%)
2025-08-08 15:16:04 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:16:04 线程3：[信息] [信息] 第六页已填入验证码: 2m7gm3 (进度: 100%)
2025-08-08 15:16:04 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:16:07 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:16:07 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-08 15:16:09 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-08 15:16:09 [信息] 开始释放1个手机号码
2025-08-08 15:16:09 [信息] [手机API] 开始批量释放1个手机号码
2025-08-08 15:16:09 [信息] [手机API] 释放手机号码: +529513658096
2025-08-08 15:16:10 [信息] [手机API] 手机号码释放成功: +529513658096
2025-08-08 15:16:10 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-08 15:16:10 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-08 15:16:11 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-08 15:16:14 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-08 15:16:14 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:16:14 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-08 15:16:14 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-08 15:16:19 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-08 15:16:19 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-08 15:16:19 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-08 15:16:19 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:16:19 线程3：[信息] [信息] 线程3验证码获取成功: 3107 (进度: 100%)
2025-08-08 15:16:19 [信息] 线程3手机号码已加入释放队列: +526863914468 (原因: 获取验证码成功)
2025-08-08 15:16:19 线程3：[信息] [信息] 线程3验证码获取成功: 3107，立即填入验证码... (进度: 100%)
2025-08-08 15:16:19 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-08 15:16:19 线程3：[信息] [信息] 线程3已自动填入手机验证码: 3107 (进度: 100%)
2025-08-08 15:16:20 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-08 15:16:20 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-08 15:16:24 线程3：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-08 15:16:24 线程3：[信息] [信息] 线程3检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-08 15:16:24 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：NO9ThWp0D5 ③AWS密码：Dpg04EIf ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-08 15:16:24 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：NO9ThWp0D5 ③AWS密码：Dpg04EIf ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:16:24 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：NO9ThWp0D5 ③AWS密码：Dpg04EIf ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:16:24 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：NO9ThWp0D5 ③AWS密码：Dpg04EIf ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:16:24 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：NO9ThWp0D5 ③AWS密码：Dpg04EIf ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:16:24 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：NO9ThWp0D5 ③AWS密码：Dpg04EIf ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:16:24 线程3：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-08 15:16:24 [信息] 线程3请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-08 15:16:24 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-08 15:16:24 [信息] 线程3失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-08 15:16:24 线程3：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-08 15:16:24 线程3：[信息] [信息] 线程3注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-08 15:16:31 [信息] 多线程窗口引用已清理
2025-08-08 15:16:31 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-08 15:16:31 [信息] 多线程管理窗口正在关闭
2025-08-08 15:16:36 [信息] 线程数量已选择: 1
2025-08-08 15:16:39 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-08 15:16:39 [信息] 开始释放1个手机号码
2025-08-08 15:16:39 [信息] [手机API] 开始批量释放1个手机号码
2025-08-08 15:16:39 [信息] [手机API] 释放手机号码: +526863914468
2025-08-08 15:16:39 [信息] [手机API] 手机号码释放成功: +526863914468
2025-08-08 15:16:40 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-08 15:16:40 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-08 15:17:07 [按钮操作] 开始注册 -> 启动注册流程
2025-08-08 15:17:07 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-08 15:17:09 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-08 15:17:09 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-08 15:17:09 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-08 15:17:09 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-08 15:17:09 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-08 15:17:10 [系统状态] 创建无痕模式上下文...
2025-08-08 15:17:12 [系统状态] 使用默认时区: America/New_York
2025-08-08 15:17:12 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-08 15:17:12 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 8核
2025-08-08 15:17:12 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=6 GB
2025-08-08 15:17:13 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-08 15:17:15 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Q3R4S5T
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 1799x1048
   • 可用区域: 1799x1008

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.49
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-08 15:17:15 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Q3R4S5T    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 1799x1048    • 可用区域: 1799x1008   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.49    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:17:15 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-08 15:17:15 [注册开始] 邮箱: <EMAIL>, 索引: 1/18
2025-08-08 15:17:15 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-08 15:17:15 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-08 15:17:15 [系统状态] 在现有窗口中新建标签页...
2025-08-08 15:17:15 [系统状态] 使用现有的浏览器上下文
2025-08-08 15:17:15 [系统状态] 正在新建标签页...
2025-08-08 15:17:16 [系统状态] 已设置页面视口大小为600x400
2025-08-08 15:17:16 [系统状态] 验证无痕Chrome模式状态...
2025-08-08 15:17:16 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-08 15:17:16 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0...
2025-08-08 15:17:16 [系统状态] 正在打开AWS注册页面...
2025-08-08 15:17:42 [系统状态] 正在执行第一页注册...
2025-08-08 15:17:42 [系统状态] 🔍 等待第一页加载完成...
2025-08-08 15:17:42 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:17:42 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:17:43 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-08 15:17:46 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-08 15:17:46 [系统状态] ✅ 未检测到IP异常错误，继续流程
2025-08-08 15:17:46 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-08-08 15:17:46 [系统状态] 🔍 第1次检测图形验证码...
2025-08-08 15:17:46 [系统状态] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-08 15:17:46 [系统状态] ✅ 第1次检测发现图形验证码！
2025-08-08 15:17:46 [系统状态] 🔍 第一页图形验证码最终检测结果: 发现验证码
2025-08-08 15:17:46 [系统状态] ⚠️ 第一页检测到图形验证码，开始处理...
2025-08-08 15:17:46 [系统状态] 第一页图形验证码自动识别模式
2025-08-08 15:17:46 [系统状态] 第一页第1次尝试自动识别图形验证码...
2025-08-08 15:17:50 [系统状态] 第一页已从iframe截取验证码图片，大小: 36175 字节
2025-08-08 15:17:50 [系统状态] ✅ 图片验证通过：201x71px，36175字节，复杂度符合要求
2025-08-08 15:17:50 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-08 15:17:54 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"cwwtcc"},"taskId":"cd26e160-7427-11f0-8e95-263b5469d4bd"}
2025-08-08 15:17:54 [系统状态] 第一页第1次识别结果: cwwtcc → 转换为小写: cwwtcc
2025-08-08 15:17:54 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-08-08 15:17:55 [系统状态] 已填入验证码: cwwtcc
2025-08-08 15:17:55 [系统状态] 已点击iframe内Submit按钮
2025-08-08 15:17:57 [系统状态] 第一页第1次图形验证码识别结果错误，等待新验证码
2025-08-08 15:17:57 [系统状态] 第一页第2次失败，等待新验证码...
2025-08-08 15:17:59 [系统状态] 第一页第2次尝试自动识别图形验证码...
2025-08-08 15:18:12 [系统状态] 第一页验证码元素等待超时，转为手动模式
2025-08-08 15:18:12 [系统状态] 第一页验证码元素等待超时，直接转为手动模式
2025-08-08 15:18:12 [系统状态] 第一页图形验证码自动处理失败，转为手动模式
2025-08-08 15:18:53 [系统状态] 检测到第一页验证码已完成，继续流程
2025-08-08 15:18:53 [系统状态] 📋 第一页图形验证码检查完成
2025-08-08 15:18:53 [系统状态] 第一页完成，等待验证码页面...
2025-08-08 15:18:53 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-08 15:18:53 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-08 15:18:53 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-08 15:19:04 [系统状态] 验证码获取成功: 409706，正在自动填入...
2025-08-08 15:19:04 [系统状态] 邮箱验证码获取成功，已取消获取线程
2025-08-08 15:19:04 [系统状态] 验证码已自动填入，正在自动点击验证按钮...
2025-08-08 15:19:04 [系统状态] 邮箱验证完成，等待页面跳转...
2025-08-08 15:19:07 [系统状态] 等待密码设置页面加载...
2025-08-08 15:19:07 [系统状态] 开始填写密码信息...
2025-08-08 15:19:09 [系统状态] 第一个密码输入框已清空并重新填写完成
2025-08-08 15:19:09 [系统状态] 确认密码输入框已清空并重新填写完成
2025-08-08 15:19:09 [系统状态] 密码填写完成，点击继续按钮...
2025-08-08 15:19:10 [系统状态] 密码设置完成，等待页面跳转...
2025-08-08 15:19:13 [系统状态] 第三页完成，进入第3.5页（账户类型确认页面）...
2025-08-08 15:19:13 [系统状态] 等待账户类型确认页面加载...
2025-08-08 15:19:14 [系统状态] 开始处理账户类型确认...
2025-08-08 15:19:20 [系统状态] 已点击Choose paid plan按钮，账户类型确认完成
2025-08-08 15:19:20 [系统状态] 账户类型确认完成，进入联系信息页面...
2025-08-08 15:19:41 [系统状态] 第3.5页完成，页面已跳转到第4页
2025-08-08 15:19:41 [系统状态] 开始后台获取手机号码，同时填写其他信息...
2025-08-08 15:19:41 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-08 15:19:41 [系统状态] 数据国家代码为CL，需要选择Chile
2025-08-08 15:19:42 [系统状态] 已点击国家/地区选择器，正在展开列表...
2025-08-08 15:19:42 [系统状态] 后台获取榴莲手机号码成功: +529363898783，已保存到注册数据
2025-08-08 15:19:43 [系统状态] 已选择国家: Chile
2025-08-08 15:19:43 [系统状态] 已成功选择国家: Chile
2025-08-08 15:19:43 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-08 15:19:43 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-08 15:19:47 [系统状态] 已选择国家代码 +52
2025-08-08 15:19:48 [系统状态] 等待后台获取的手机号码结果...
2025-08-08 15:19:48 [系统状态] 已自动获取并填入手机号码: +529363898783
2025-08-08 15:19:49 [系统状态] 使用已获取的手机号码: +529363898783（保存本地号码: 9363898783）
2025-08-08 15:19:49 [系统状态] 联系信息完成，等待页面加载...
2025-08-08 15:19:53 [系统状态] 进入付款信息页面...
2025-08-08 15:19:53 [系统状态] 正在选择月份: March
2025-08-08 15:19:53 [系统状态] 已选择月份（标准选项）: March
2025-08-08 15:19:54 [系统状态] 正在选择年份: 2030
2025-08-08 15:19:54 [系统状态] 已选择年份（标准选项）: 2030
2025-08-08 15:19:55 [系统状态] 付款信息完成，进入验证码验证页面...
2025-08-08 15:19:55 [系统状态] 开始填写验证码验证页面...
2025-08-08 15:19:55 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-08 15:20:01 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-08 15:20:02 [系统状态] 已选择国家代码: +52
2025-08-08 15:20:02 [系统状态] 已清空并重新填写手机号码: 9363898783
2025-08-08 15:20:02 [系统状态] 已点击发送验证码按钮
2025-08-08 15:20:04 [系统状态] 🔍 检查是否出现验证手机区号错误...
2025-08-08 15:20:04 [系统状态] ✅ 未检测到验证手机区号错误，继续执行
2025-08-08 15:20:04 [系统状态] 手机号码自动模式 + 图形验证码自动模式：开始自动处理...
2025-08-08 15:20:04 [系统状态] 自动模式：开始处理图形验证码...
2025-08-08 15:20:04 [系统状态] 第六页点击发送验证码后，等待图形验证码出现...
2025-08-08 15:20:07 [系统状态] 第六页图形验证码自动识别模式，开始处理...
2025-08-08 15:20:07 [系统状态] 第六页第1次尝试自动识别图形验证码...
2025-08-08 15:20:11 [系统状态] 第六页已从iframe截取验证码图片，大小: 35450 字节
2025-08-08 15:20:11 [系统状态] ✅ 图片验证通过：201x71px，35450字节，复杂度符合要求
2025-08-08 15:20:11 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-08 15:20:12 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"b5r3zx"},"taskId":"1f4b12fe-7428-11f0-9526-c60a782a5790"}
2025-08-08 15:20:12 [系统状态] 第六页第1次识别结果: b5r3zx → 转换为小写: b5r3zx
2025-08-08 15:20:12 [系统状态] 第六页使用iframe内GetByLabel选择器
2025-08-08 15:20:12 [系统状态] 第六页已填入验证码: b5r3zx
2025-08-08 15:20:12 [系统状态] 第六页已点击iframe内Submit按钮
2025-08-08 15:20:15 [系统状态] 第1次图形验证码识别成功
2025-08-08 15:20:15 [系统状态] 第六页图形验证码自动完成，检查验证结果...
2025-08-08 15:20:18 [系统状态] 第六页图形验证码验证成功，进入第七页
2025-08-08 15:20:21 [系统状态] 开始处理第七页 - Continue (step 4 of 5) 页面
2025-08-08 15:20:21 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-08 15:20:21 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-08 15:20:21 [系统状态] 第七页自动模式：等待5秒后开始获取验证码...
2025-08-08 15:20:26 [系统状态] 开始自动获取验证码，2分钟超时...
2025-08-08 15:20:26 [系统状态] 第1次尝试获取验证码...（剩余4次尝试）
2025-08-08 15:20:26 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-08 15:20:26 [系统状态] 成功获取验证码: 6555，立即填入验证码...
2025-08-08 15:20:26 [系统状态] 第七页：找到验证码输入框 (Verify code)
2025-08-08 15:20:26 [系统状态] 已自动填入手机验证码: 6555
2025-08-08 15:20:26 [系统状态] 榴莲手机号码已加入黑名单
2025-08-08 15:20:27 [系统状态] 正在自动点击Continue按钮...
2025-08-08 15:20:27 [系统状态] 手机验证码验证完成，继续执行后续步骤...
2025-08-08 15:20:30 [系统状态] 检测到无资格错误提示: You are not eligible for new customer credits
2025-08-08 15:20:30 [系统状态] 检测到卡号已被关联错误，注册失败
2025-08-08 15:20:30 [系统状态] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：15IhQB81 ③AWS密码：MU0bWE42 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:20:30 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：15IhQB81 ③AWS密码：MU0bWE42 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:20:30 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：15IhQB81 ③AWS密码：MU0bWE42 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-08 15:20:30 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-08 15:20:30 [系统状态] 已通知保存失败数据，失败原因: 卡号已被关联
2025-08-08 15:20:58 [系统状态] 注册失败，数据已归类，注册流程终止
2025-08-08 15:21:06 [信息] 线程数量已选择: 2
2025-08-08 15:22:16 [按钮操作] 开始注册 -> 启动注册流程
2025-08-08 15:22:16 [信息] 开始启动多线程注册，线程数量: 2
2025-08-08 15:22:16 [信息] 多线程管理器已重新创建
2025-08-08 15:22:16 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 17
2025-08-08 15:22:16 [信息] 所有线程已停止并清理
2025-08-08 15:22:16 [信息] 正在初始化多线程服务...
2025-08-08 15:22:16 [信息] 榴莲手机API服务已初始化
2025-08-08 15:22:16 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-08 15:22:16 [信息] 多线程服务初始化完成
2025-08-08 15:22:16 [信息] 数据分配完成：共17条数据分配给2个线程
2025-08-08 15:22:16 [信息] 线程1分配到9条数据
2025-08-08 15:22:16 [信息] 线程2分配到8条数据
2025-08-08 15:22:16 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:22:16 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:22:16 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:22:16 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:22:16 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=16 GB
2025-08-08 15:22:16 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:22:16 [信息] 屏幕工作区域: 1280x672
2025-08-08 15:22:16 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-08 15:22:16 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-08 15:22:16 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:22:16 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=64 GB
2025-08-08 15:22:16 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-08 15:22:16 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=CL, 时区=America/Santiago
2025-08-08 15:22:16 [信息] 多线程注册启动成功，共2个线程
2025-08-08 15:22:16 线程1：[信息] 开始启动注册流程
2025-08-08 15:22:16 线程2：[信息] 开始启动注册流程
2025-08-08 15:22:16 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-08 15:22:16 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:22:16 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-08 15:22:16 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-08 15:22:16 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:22:16 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-08 15:22:16 [信息] 多线程管理窗口已初始化
2025-08-08 15:22:16 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:22:16 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-08 15:22:16 [信息] 多线程管理窗口已打开
2025-08-08 15:22:16 [信息] 多线程注册启动成功，共2个线程
2025-08-08 15:22:21 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:22:21 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-08 15:22:21 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:22:21 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:22:21 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-08 15:22:21 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:22:21 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:22:21 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-08 15:22:21 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-08 15:22:21 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-08 15:22:21 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-08 15:22:21 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-08 15:22:22 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:22:22 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-08 15:22:24 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:22:24 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:22:24 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-08 15:22:24 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-08 15:22:24 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-08 15:22:24 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-08 15:22:24 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 4核 (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=64 GB
2025-08-08 15:22:24 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 20核 (进度: 0%)
2025-08-08 15:22:24 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=16 GB
2025-08-08 15:22:25 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:22:25 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-08 15:22:26 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-G5H6I7J
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1989x1020
   • 可用区域: 1989x980

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.55
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:22:26 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-G5H6I7J    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1989x1020    • 可用区域: 1989x980   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.55    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-08 15:22:26 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-08 15:22:26 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:22:26 线程1：[信息] 浏览器启动成功
2025-08-08 15:22:26 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:22:27 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:22:27 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: disabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 1790x1034
   • 可用区域: 1790x994

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.53
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-08 15:22:27 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: disabled   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 1790x1034    • 可用区域: 1790x994   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.53    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-08 15:22:27 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-08 15:22:27 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-08 15:22:27 线程2：[信息] 浏览器启动成功
2025-08-08 15:22:27 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-08 15:22:27 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-08 15:22:27 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:22:27 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-08 15:22:27 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-08 15:22:52 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-08 15:22:52 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-08 15:22:52 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-08 15:22:52 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-08 15:22:52 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-08 15:22:52 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-08 15:22:55 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-08 15:22:55 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-08 15:22:55 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-08 15:22:59 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34969 字节 (进度: 100%)
2025-08-08 15:22:59 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34969字节，复杂度符合要求 (进度: 100%)
2025-08-08 15:22:59 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-08 15:23:01 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"gpgxrt"},"taskId":"83fceb96-7428-11f0-8a3b-46c0802b4ba0"} (进度: 100%)
2025-08-08 15:23:01 线程1：[信息] [信息] 第一页第1次识别结果: gpgxrt → 转换为小写: gpgxrt (进度: 100%)
2025-08-08 15:23:01 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-08 15:23:01 线程1：[信息] [信息] 已填入验证码: gpgxrt (进度: 100%)
2025-08-08 15:23:01 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-08 15:23:03 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-08 15:23:03 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:23:03 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-08 15:23:03 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-08 15:23:05 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-08 15:23:05 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-08 15:23:05
2025-08-08 15:23:08 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-08 15:23:08 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-08 15:23:08
2025-08-08 15:23:11 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-08 15:23:11 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-08 15:23:11
2025-08-08 15:23:14 [信息] [线程1] 邮箱验证码获取成功: 636200，立即停止重复请求
2025-08-08 15:23:14 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-08 15:23:14 [信息] [线程1] 已清理响应文件
2025-08-08 15:23:14 线程1：[信息] [信息] 验证码获取成功: 636200，正在自动填入... (进度: 25%)
2025-08-08 15:23:14 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-08 15:23:14 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-08 15:23:14 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-08 15:23:14 [信息] 线程1完成第二页事件已处理
2025-08-08 15:23:14 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-08 15:23:14 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-08 15:23:17 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-08 15:23:17 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-08 15:23:17 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-08 15:23:17 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-08 15:23:17 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-08 15:23:18 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-08 15:23:21 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-08 15:23:21 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-08 15:23:21 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-08 15:23:26 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-08 15:23:26 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-08 15:23:28 线程2：[信息] [信息] 所有自动线程已停止 (进度: 98%)
2025-08-08 15:23:28 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 98%)
2025-08-08 15:23:28 线程2：[信息] 已暂停
2025-08-08 15:23:28 [信息] 线程2已暂停
2025-08-08 15:23:28 [信息] 线程2已暂停
2025-08-08 15:23:47 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-08 15:23:47 [信息] 线程1获取已分配的榴莲手机号码: +526648328770
2025-08-08 15:23:47 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +526648328770 (进度: 38%)
2025-08-08 15:23:47 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-08 15:23:48 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-08 15:23:50 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-08 15:23:50 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-08 15:23:50 线程1：[信息] [信息] 正在选择国家代码 +56 (智利 (Chile) +56)... (进度: 38%)
2025-08-08 15:23:50 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-08 15:23:52 线程1：[信息] [信息] 已选择国家代码 +56 (进度: 38%)
2025-08-08 15:23:53 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-08 15:23:53 线程1：[信息] [信息] 已自动获取并填入手机号码: +526648328770 (进度: 38%)
2025-08-08 15:23:54 线程1：[信息] [信息] 使用已获取的手机号码: +526648328770（保存本地号码: +526648328770） (进度: 38%)
2025-08-08 15:23:54 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-08 15:23:57 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-08 15:24:02 线程1：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-08-08 15:24:02 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-08-08 15:24:02 线程1：[信息] 已暂停
2025-08-08 15:24:02 [信息] 线程1已暂停
2025-08-08 15:24:02 [信息] 线程1已暂停
2025-08-08 15:24:25 线程1：[信息] [信息] 正在选择月份: July (进度: 38%)
2025-08-08 15:24:25 线程1：[信息] [信息] 已选择月份（标准选项）: July (进度: 38%)
2025-08-08 15:24:26 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-08-08 15:24:27 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-08-08 15:24:27 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 38%)
2025-08-08 15:24:27 线程1：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-08 15:24:27 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-08 15:24:27 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-08 15:24:28 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify and continue (step 3 of 5)' → 第5页 (进度: 38%)
2025-08-08 15:24:28 线程1：[信息] [信息] ✅ 直接确认为第5页 (进度: 100%)
2025-08-08 15:24:28 线程1：[信息] [信息]  智能检测到当前在第5页 (进度: 100%)
2025-08-08 15:24:28 线程1：[信息] [信息] 智能检测到当前在第5页，开始智能处理... (进度: 100%)
2025-08-08 15:24:29 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-08 15:24:29 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-08 15:24:29 线程1：[信息] [信息] 正在选择国家代码 +56 (智利 (Chile) +56)... (进度: 100%)
2025-08-08 15:24:32 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-08 15:24:32 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-08 15:24:32 线程1：[信息] 已暂停
2025-08-08 15:24:32 [信息] 线程1已暂停
2025-08-08 15:24:32 [信息] 线程1已暂停
2025-08-08 15:24:35 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-08 15:24:36 线程1：[信息] [信息] 已选择国家代码: +56 (进度: 100%)
2025-08-08 15:24:36 线程1：[信息] [信息] 检测到注册已暂停或终止，停止第六页手机验证处理 (进度: 100%)
2025-08-08 15:24:59 线程1：[信息] [信息] 继续注册失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Expiration date Month" }) (进度: 100%)
2025-08-08 15:24:59 线程1：[信息] 已继续
2025-08-08 15:24:59 [信息] 线程1已继续
2025-08-08 15:26:29 线程2：[信息] [信息] 注册失败: Target page, context or browser has been closed (进度: 98%)
2025-08-08 15:26:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:29 [信息] 多线程状态已重置
2025-08-08 15:26:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:29 [信息] 多线程状态已重置
2025-08-08 15:26:29 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：K7p34456TSk ③AWS密码：iDkMB4cA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-08 15:26:29 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：K7p34456TSk ③AWS密码：iDkMB4cA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-08 15:26:29 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：K7p34456TSk ③AWS密码：iDkMB4cA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-08 15:26:29 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：K7p34456TSk ③AWS密码：iDkMB4cA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-08 15:26:29 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：K7p34456TSk ③AWS密码：iDkMB4cA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-08 15:26:29 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：K7p34456TSk ③AWS密码：iDkMB4cA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-08 15:26:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:29 [信息] 多线程状态已重置
2025-08-08 15:26:29 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-08 15:26:29 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-08 15:26:33 [信息] 获取线程1当前数据: <EMAIL>
2025-08-08 15:26:33 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-08 15:26:33 线程1：[信息] 数据详情: <EMAIL>|vjHu6qyl|Carlos Cruz|Enel Chile|4350 Avenida Las Condes|Quilpue|VS|2430000|5331870046760687|07|29|520|Carlos Cruz|3379ORWMz87|CL
2025-08-08 15:26:33 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-08 15:26:33 [信息] 手动终止密钥检测 - _accessKey: '', _secretAccessKey: '', _currentData.AccessKey: '', _currentData.SecretAccessKey: '', _currentData.MfaInfo: ''
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] [信息] 未检测到任何密钥信息，复制基础注册数据 (进度: 100%)
2025-08-08 15:26:33 [信息] 手动终止 - 未检测到任何密钥信息，复制基础注册数据
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3379ORWMz87 ③AWS密码：vjHu6qyl ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-08 15:26:33 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3379ORWMz87 ③AWS密码：vjHu6qyl ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-08 15:26:33 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3379ORWMz87 ③AWS密码：vjHu6qyl ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-08 15:26:33 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3379ORWMz87 ③AWS密码：vjHu6qyl ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-08 15:26:33 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3379ORWMz87 ③AWS密码：vjHu6qyl ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-08 15:26:33 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3379ORWMz87 ③AWS密码：vjHu6qyl ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] [信息] 多线程模式 - 跳过重复数据复制 (进度: 100%)
2025-08-08 15:26:33 [信息] 多线程模式 - 跳过重复数据复制，数据已在CopyTerminatedRegistrationInfoAsync中处理
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-08 15:26:33 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250808_152216
2025-08-08 15:26:33 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-08 15:26:33 [信息] 多线程状态已重置
2025-08-08 15:26:33 线程1：[信息] 已终止
2025-08-08 15:26:33 [信息] 线程1已终止
2025-08-08 15:26:33 [信息] 开始处理线程1终止数据，共1个数据
2025-08-08 15:26:33 [信息] 处理线程1终止数据: <EMAIL>
2025-08-08 15:26:33 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-08 15:26:33 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-08 15:26:33 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-08 15:26:33 [信息] UniformGrid列数已更新为: 1
2025-08-08 15:26:33 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-08 15:26:33 [信息] 线程1已终止
2025-08-08 15:26:34 [信息] 多线程窗口引用已清理
2025-08-08 15:26:34 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-08 15:26:34 [信息] 多线程管理窗口正在关闭
